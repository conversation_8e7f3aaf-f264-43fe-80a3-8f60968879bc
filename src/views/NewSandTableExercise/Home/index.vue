<template>
	<div class="new-sandtable-exercise">
		<div class="menu-box">
			<template v-for="(item, index) in menuList" :key="index">
				<div class="history-card" v-if="index === 0">
					<div class="top">
						<img :src="item.img" alt="" />
						<div class="info-card">
							<div class="label">当前方案：</div>
							<div class="plan-name">{{ homepageCount.Pms_mock.mock_name }}</div>
						</div>
					</div>
					<div class="bottom">
						<a @click="onPlanList">所有方案>></a>
					</div>
				</div>
				<div class="menu" v-else :style="{ backgroundImage: `url(${item.bgImg})` }">
					<div class="inner">
						<img class="icon" :src="item.img" />
						<div class="right">
							<div class="label">{{ item.label }}</div>
							<div
								class="router-text"
								@click="
									() => {
										item.click ? item.click() : onRouterLink(item.path)
									}
								"
							>
								{{ item.routerText }}
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<div class="content-box">
			<div :class="`left ${!sliderStatus && 'translate-animation'}`" @transitionend="onTransitionEnd">
				<div class="scroll-box">
					<OrgTree @select="onSelect" @bread-crumb="onBreadCrumb" :initKey="initKey" ref="treeRef" :type="1" />
				</div>
				<div :class="['slider-control', !sliderStatus && 'slider-hidden']" @click="onExpand"></div>
			</div>
			<div class="right">
				<div class="breadcrumb-box">
					<a-breadcrumb separator=">" class="breadcrumb">
						<a-breadcrumb-item v-for="item in breadCrumb" :key="item.org_id">{{ item.name }}</a-breadcrumb-item>
					</a-breadcrumb>
				</div>
				<div class="user-box" v-if="userInfoList.user_list?.length">
					<div class="org-container">
						<div class="bottom-box">
							<div class="flex">
								<div class="group">
									班子结构:
									<svg
										t="1697450179918"
										class="w-39 m-left-18 icon"
										viewBox="0 0 1024 1024"
										version="1.1"
										xmlns="http://www.w3.org/2000/svg"
										p-id="4025"
									>
										<path
											v-if="userInfoList.user_list?.length"
											d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
											:fill="cardColor[userInfoList.org_screen || 0]"
											p-id="4026"
										></path>
									</svg>
								</div>
								<div class="group">
									班子职数: <span class="red-text">缺配{{ userInfoList.org_lack_num }}</span>
								</div>
							</div>
							<div class="btn-box">
								<a-button type="primary" @click="onUserSelect" v-if="type != 3">前往调整</a-button>
							</div>
						</div>
						<div class="lf-user-box" ref="scrollContainer">
							<div :class="`user-item`" v-for="(item1, index) in userInfoList.user_list" :key="item1.user_id">
								<CodeAvatar :user_id="item1.user_id" :head_url="item1.head_url">
									<template #avatar="{ avatar }">
										<img :src="avatar" class="avatar" />
									</template>
								</CodeAvatar>
								<div class="user-name">{{ item1.user_name || '---' }}</div>
								<div class="user-position">{{ item1.position }}</div>
							</div>

							<!-- <div :class="`user-item ${pageStatus.newAdd ? 'active-select' : ''}`" @click="onAdd" v-if="userInfoList.vacancy">
							<div class="img-box">
								<img src="../images/vacancy.png" class="vacancy" />
							</div>
							<div class="user-name">---</div>
							<div class="user-position">---</div>
						</div> -->
						</div>
					</div>
				</div>
				<div class="search-box" v-if="currentOrg.orgId === 1">
					<a-form class="search-form" layout="vertical">
						<a-form-item label="要调整的干部">
							<AutoComplete
								placeholder="请输入"
								:value="searchState.cadre.label"
								@change="onCadreChange"
								:options="cadreOption"
								@search="onCadreSearch"
							>
								<template #option="data">
									<div class="select-option" @click="onCadreSelect(data)">{{ data.label }} {{ data.org_name }}</div>
								</template>
							</AutoComplete>
						</a-form-item>
						<a-form-item label="要调整的班子">
							<AutoComplete placeholder="请输入" :value="searchState.unit.label" :options="unitOption" @change="onUnitChange" @search="onUnitSearch">
								<template #option="data">
									<div class="select-option" @click="onUnitSelect(data)">{{ data.label }}</div>
								</template>
							</AutoComplete>
						</a-form-item>
						<a-form-item :wrapper-col="{ span: 24 }">
							<a-button type="primary" class="btn-submit" @click="onToChange">调整</a-button>
						</a-form-item>
					</a-form>
				</div>
			</div>
		</div>
		<div class="loading" v-show="loading">
			<a-spin size="large" :indicator="indicator" />
		</div>
	</div>
</template>

<script lang="ts">
const createState = (): {
	currentOrg: any
	currentOrgId: any
	activeKey: any
	breadCrumb: any[]
	subTreeData: any[]
} => ({
	currentOrg: undefined,
	currentOrgId: undefined,
	activeKey: undefined,
	breadCrumb: [],
	subTreeData: [],
})
// 页面保活
const page_cache = {
	isClear: false,
	mock_id: undefined,
	state: createState(),
}

export default {
	name: 'newSandTableExerciseHome',
}
</script>
<script lang="ts" setup>
import OrgTree from '@/components/OrgTree.vue'
import useMock from '@/store/newSandbox'
import useOrg from '@/store/org'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { computed, h, inject, onActivated, onUnmounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dongyiPng from '../images/dongyi.png'
import historyPng from '../images/history.png'
import preWarnPng from '../images/prewarn.png'
import vancyPng from '../images/vancy.png'
import waitPng from '../images/wait.png'
import warnPng from '../images/warn.png'

import dongyiBg from '../images/dongyibg.png'
import preWarnBg from '../images/pre-warn-bg.png'
import vancyBg from '../images/vancy-bg.png'
import waitBg from '../images/wait-bg.png'
import warnBg from '../images/warn-bg.png'

import { getHomeData, getHomePage, homePageOrgSearch, homePageUserSearch } from '@/apis/new-sand-table-exercise'
import CodeAvatar from '@/components/CodeAvatar.vue'
import { AutoComplete } from 'ant-design-vue'

const pageApi: any = inject('pageApi')

const org = useOrg()

const router = useRouter()
const route = useRoute()

// 当type === 1 表示暂存状态 不支持操作： 另存
let { mock_id, mock_name, type } = route.query as any

const mock = useMock()

mock_id && mock.update(mock_id)
// 方案id
const mockId = ref(mock_id || mock.mockId)

sessionStorage.setItem('mock_id', mockId.value)

if (page_cache.mock_id !== mockId.value) {
	// page_cache.state = createState()
}

page_cache.mock_id = mockId.value
// 默认清空
page_cache.isClear = true

const cardColor = ['#60ca71', '#f6dd00', '#ffa300', '#ff473e']
// 数量相关
const homepageCount = ref({
	abnormal_org_screen: 0,
	Pms_mock: {
		create_time: undefined,
		mock_id: undefined,
		update_time: undefined,
		update_user: undefined,
		mock_name: undefined,
		create_user: undefined,
		device: undefined,
		status: undefined,
	},
	org_screen: 0,
	motion_num: 0,
	org_lack_num: 0,
	alert_num: 0,
	to_submit_num: 0,
	lack_num: 0,
})
// 过度效果结束
const animationEnd = ref(true)
// 侧边栏是否展开
const sliderStatus = ref(true)
//
const _initKey = page_cache.state.activeKey

const initKey = ref(_initKey)

const treeRef = ref<any>()

const cadreOption = ref([])

const searchState = reactive<any>({
	cadre: {},
	unit: {},
})

const unitOption = ref([])

const loading = ref(false)

const userInfoList = ref<any>({
	org_lack_num: 0,
	org_screen: 0,
	user_list: [],
})

const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '34px',
	},
	spin: true,
})
const { org_id, short_name } = org.orgTree?.[0]

const currentOrg = ref(page_cache.state.currentOrg || { orgId: org_id, orgName: short_name })

const menuList = computed(() => [
	{
		label: homepageCount.value.Pms_mock.mock_name,
		img: historyPng,
		routerText: '历史方案',
		path: '/sand-table-exercise/historical-plan',
	},
	{
		label: homepageCount.value.motion_num,
		img: dongyiPng,
		routerText: '动议名单',
		bgImg: dongyiBg,
		path: '/new-sand-table-exercise/adjust-list',
	},
	{
		label: homepageCount.value.lack_num,
		img: vancyPng,
		routerText: '缺配职数',
		bgImg: vancyBg,
		path: '/new-sand-table-exercise/lack-of-matching',
	},
	{
		label: homepageCount.value.abnormal_org_screen,
		img: warnPng,
		routerText: '班子结构异常',
		bgImg: warnBg,
		path: '/new-sand-table-exercise/structure-abnormal',
	},
	{
		label: homepageCount.value.to_submit_num,
		img: waitPng,
		routerText: '待配名单',
		bgImg: waitBg,
		path: '/new-sand-table-exercise/wait-list',
	},
	{
		label: homepageCount.value.alert_num,
		img: preWarnPng,
		routerText: '岗位预警',
		bgImg: preWarnBg,
		path: '/new-sand-table-exercise/early-warning',
	},
])
const breadCrumb = ref<any>(page_cache.state.breadCrumb)
// 清理所有页面缓存
pageApi?.routerClearCache()

// 路由跳转
const onRouterLink = (path: any, query = {}) => {
	if (!mockId.value) return

	// page_cache.state.currentOrg = currentOrg.value

	const { subTreeData } = treeRef.value

	// page_cache.state.subTreeData = subTreeData

	// page_cache.isClear = false

	pageApi.routerPushCache('newSandTableExerciseHome')

	if (path) {
		router.push({
			path,
			query: {
				mock_id: mockId.value,
				mock_name: homepageCount.value.Pms_mock.mock_name,
				...query,
			},
		})
	}
}
const onTransitionEnd = () => {
	animationEnd.value = !animationEnd.value
}
const onSelect = (org_id: any, org: any, expandedKeys: any) => {
	if (!org_id || org_id === currentOrg.value.orgId) return

	currentOrg.value = {
		orgId: org_id,
		orgName: org.name,
	}

	currentPage1.value = 1
	userInfoList.value = []

	// page_cache.state.activeKey = expandedKeys

	loadOrgData()
}

const onCadreChange = (e: any) => {
	if (typeof e === 'number') return

	if (!e) {
		return (searchState.cadre = {})
	}

	searchState.cadre.label = e
}
const onUnitChange = (e: any) => {
	if (typeof e === 'number') return
	if (!e) {
		return (searchState.unit = {})
	}
	searchState.unit.label = e
}

const onExpand = () => {
	sliderStatus.value = !sliderStatus.value
}

const onBreadCrumb = (bread: any, type: string) => {
	// if (type === '1' && page_cache.state.breadCrumb.length) {
	// 	breadCrumb.value = page_cache.state.breadCrumb || bread
	// } else {
	breadCrumb.value = bread
	// }

	// page_cache.state.breadCrumb = bread
}
/**
 * @description: 缺配跳转
 * @param {*} org
 * @param {*} job
 * @return {*}
 */
const onVacancy = (org: any) => {
	const { orgId, orgName } = currentOrg.value

	const { pmsJobId, jobName } = org

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
		job_id: pmsJobId,
		job_name: jobName,
		from: 'candidate',
	}

	onRouterLink('/new-sand-table-exercise/org-team', query)
}

const onLookFor = (user?: any) => {
	const { orgId, orgName } = currentOrg.value

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
		from: 'candidate',
	}

	if (user) {
		const { userId } = user
		query['user_id'] = userId
	}

	onRouterLink('/new-sand-table-exercise/org-team', query)
}
const onToChange = () => {
	const { unit, cadre } = searchState

	if (unit.org_id) {
		return onRouterLink('/new-sand-table-exercise/org-team', {
			org_id: unit.org_id,
			org_name: unit.org_name,
			mock_id: mockId.value,
			from: 'candidate',
		})
	}

	if (cadre.user_id) {
		return onRouterLink('/new-sand-table-exercise/org-team', {
			org_id: cadre.org_id,
			org_name: cadre.org_name,
			mock_id: mockId.value,
			from: 'candidate',
			user_id: cadre.user_id,
		})
	}
}

const onUserSelect = (user: any) => {
	if (type == 3) {
		return
	}

	if (!user.user_id) {
		onVacancy(user)
	} else {
		onLookFor(user)
	}
}

const onPlanList = () => {
	onRouterLink(`/new-sand-table-exercise/plan-list`)
}

// 加载班子队伍
const loadOrgData = async () => {
	const res = await getHomeData({ org_id: currentOrg.value.orgId, mock_id: mockId.value })

	if (res.code === 0) {
		userInfoList.value = res.data
	}
}

const loadData = async (_loading = true) => {
	loading.value = _loading
	try {
		const res = await getHomePage({ mock_id: mockId.value })
		if (res.code === 0) {
			homepageCount.value = res.data

			mockId.value || (mockId.value = res.data.Pms_mock.mock_id)

			loadOrgData()
		}
	} catch (e) {
	} finally {
		loading.value = false
	}
}

loadData()

onActivated(() => {
	if (route.query.mock_id && route.query.mock_id !== mockId.value) {
		pageApi.onRefreshPage()
	}

	pageApi?.routerRemoveCache('newSandTableExerciseHome')

	loadData(false)
})

// onMounted(() => {
// 	if (page_cache.state.subTreeData) {
// 		treeRef.value.subTreeData = page_cache.state.subTreeData
// 	}
// })

const onCadreSelect = (value: any) => {
	console.log('onCadreSelect', value)
	searchState.cadre = value
}

const onUnitSelect = (value: any) => {
	console.log('onUnitSelect', value)
	searchState.unit = value
}

const onCadreSearch = async (value: any) => {
	console.log('onCadreSearch', value)

	const res = await homePageUserSearch({ name: value })

	if (res.code === 0) {
		const data = res.data

		cadreOption.value = data.map((item: any, index: number) => {
			return {
				...item,
				label: item.user_name,
				value: item.user_id,
			}
		})
	}
}
const onUnitSearch = async (value: any) => {
	console.log('onCadreSearch', value)

	const res = await homePageOrgSearch({ name: value })

	if (res.code === 0) {
		const data = res.data

		unitOption.value = data.map((item: any, index: number) => {
			return {
				...item,
				label: item.org_name,
				value: item.org_id,
			}
		})
	}
}
onUnmounted(() => {
	if (page_cache.isClear) {
		// page_cache.state = createState()
	}
})

const scrollContainer = ref()
const currentPage1 = ref(1)
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.white-bg {
	background: #ffffff;
}
.flex {
	display: flex;
}

.new-sandtable-exercise {
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	overflow: hidden;
	.menu-box {
		display: flex;
		width: 100%;
		padding: 12px;
		gap: 12px;
		background-color: #ffffff;
		.history-card {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			width: 371px;
			height: 143px;
			background: url('../images/history-bg.png') center / cover no-repeat;
			.top {
				display: flex;
				align-items: center;
				flex: 1;
				img {
					width: 70px;
					height: 70px;
				}
				.info-card {
					margin-left: 3px;
					.label {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 23px;
						color: #000000;
						line-height: 26px;
					}
					.plan-name {
						min-height: 20px;
						margin-top: 6px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						font-size: 24px;
						color: #000000;
						line-height: 28px;
					}
				}
			}
			.bottom {
				display: flex;
				align-items: center;
				justify-content: center;
				border-top: 1px solid rgba(0, 0, 0, 0.1);
				height: 42px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 21px;
				line-height: 21px;
				color: #008eff;
				text-align: center;
			}
		}
		.menu {
			height: 143px;
			overflow: hidden;
			flex: 1;
			background: center / cover no-repeat;
			&:nth-child(2n) .inner {
				background-image: url('../images/menu-box-2.png');
			}
			.inner {
				padding: 0px 30px;
				display: flex;
				align-items: center;
				flex: 1;
				height: 100%;
				background: url('../images/menu-box-1.png') no-repeat center / 100%;

				.icon {
					width: 51px;
					height: 51px;
				}
				.right {
					margin-left: 18px;
					.label {
						font-size: 41px;
						line-height: 47px;
						font-family: DIN, DIN;
						font-weight: bold;
						color: #000000;
					}
					.router-text {
						margin-top: 3px;
						display: flex;
						align-items: center;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 23px;
						line-height: 23px;
						color: rgba(0, 0, 0, 0.85);
						cursor: pointer;
						&::after {
							content: '';
							display: inline-block;
							width: 18px;
							height: 18px;
							background: url('../images/right.png') no-repeat center / 100%;
						}
					}
				}
			}
			.tips {
				padding: 0px 30px;
				margin-top: -27px;
				font-size: 19px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.65);
				line-height: 22px;
			}
		}
	}
	.content-box {
		margin-top: 12px;
		position: relative;
		display: flex;
		flex: 1;
		overflow: hidden;
		background: #f5f5f5;
		.translate-animation {
			position: absolute !important;
			transform: translate(-100%);
			z-index: 10;
		}
		.slider-control {
			position: absolute;
			right: 0;
			top: 40%;
			width: 30px;
			height: 56px;
			background: url(@/assets/images/slider-icon.png) no-repeat center / cover;
			cursor: pointer;
		}
		.slider-hidden {
			transform: rotateZ(180deg) translate(-100%);
		}
		.left {
			padding: 15px 12px;
			position: relative;
			height: 100%;
			transition: transform 0.3s ease-in-out;
			.scroll-box {
				height: 100%;
				overflow: auto;
			}
			background: #ffffff;
		}
		.right {
			display: flex;
			flex-direction: column;
			margin-left: 12px;
			flex: 1;
			height: 100%;
			background: #ffffff;
			.breadcrumb-box {
				padding: 15px 21px;
				border-bottom: 1px solid rgba(0, 0, 0, 0.2);
				:deep(.ant-breadcrumb-link) {
					font-size: 24px;
				}
				:deep(.ant-breadcrumb-separator) {
					font-size: 24px;
				}
			}
			.user-box {
				flex: 1;
				overflow: hidden;
				.org-container {
					display: flex;
					flex-direction: column;
					width: 100%;
					height: 100%;
					padding: 0px 0px;
					background: #ffffff;
					overflow: hidden;
					.bottom-box {
						padding: 18px 24px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						background: #fcf6ec;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						.group {
							display: flex;
							align-items: center;
							font-size: 24px;
							line-height: 24px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #000000;
							&:not(:last-child) {
								margin-right: 39px;
							}
							&::before {
								content: '';
								margin-right: 9px;
								display: inline-block;
								width: 12px;
								height: 12px;
								background: #ff9900;
								border-radius: 50%;
								opacity: 1;
							}
							.red-text {
								margin-left: 16px;
								color: #ff0000;
							}
							.yellow-text {
								color: #ff9900;
							}
							.btn-box {
								button {
									width: 120px;
									height: 48px;
									font-size: 21px;
									background: #008eff;
									border-radius: 3px 3px 3px 3px;
									opacity: 1;
									color: #ffffff;
								}
							}
						}
						.link {
							float: right;
							font-size: 18px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #008eff;
							cursor: pointer;
						}
					}

					.lf-user-box {
						padding: 24px;
						.white-bg;
						display: flex;
						flex-wrap: wrap;
						align-content: flex-start;

						flex: 1;
						overflow-y: auto;
						gap: 0px 20px;

						.user-item {
							.white-bg;
							display: flex;
							align-items: center;
							flex-direction: column;
							width: 182px;
							height: 299px;
							// overflow: hidden;
							border-radius: 3px 3px 3px 3px;
							opacity: 1;
							.avatar {
								width: 127px;
								height: 159px;
								object-fit: fill;
							}
							.img-box {
								.flex-center;
								width: 127px;
								height: 159px;
								background-color: #eeeeee;
								.vacancy {
									width: 41px;
									height: 41px;
									object-fit: contain;
								}
							}
							.user-name {
								margin-top: 10px;
								text-align: center;
								font-size: 24px;
								line-height: 28px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 500;
								color: #000000;
							}
							.user-position {
								margin-top: 10px;
								text-align: center;
								font-size: 21px;
								line-height: 25px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 400;
								color: rgba(0, 0, 0, 0.9);
								// 超过两行省略
								display: -webkit-box;
								text-overflow: -o-ellipsis-lastline;
								overflow: hidden;
								text-overflow: ellipsis;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 3;
							}
						}
						.active-select {
							background: #edf7ff;
							.user-position {
								color: #008eff;
							}
							.user-name {
								color: #008eff;
							}
						}
					}
				}
			}
			.search-box {
				display: flex;
				justify-content: center;
				flex: 1;
				background: url('../images/home-page.png') no-repeat center / cover;

				.search-form {
					margin-top: 100px;
					height: 300px;

					:deep(.ant-form-item-label > label) {
						font-weight: 400;
						font-size: 24px;
						color: #000000;
						line-height: 28px;
					}
					:deep(.ant-select-selector) {
						width: 585px;
						height: 66px;
						font-size: 24px;
						input {
							height: 100%;
						}
						.ant-select-selection-placeholder {
							height: 100%;
							line-height: 66px;
						}
					}
					.btn-submit {
						height: 60px;
						width: 100%;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						font-size: 21px;
						color: #ffffff;
					}
				}
			}
		}
	}
}

.loading {
	position: absolute;
	inset: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.4);
}
</style>
<style>
.select-option {
	padding: 10px 0px;
	font-size: 20px !important;
}
</style>
