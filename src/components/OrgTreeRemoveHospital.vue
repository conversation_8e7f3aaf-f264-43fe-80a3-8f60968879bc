<template>
	<div :class="`tree-list`">
		<div class="left">
			<div class="top-org" @click="onTopSelect"><img class="org-icon" src="@/assets/images/org-icon.png" alt="" />{{ treeData[0]?.short_name }}</div>
			<a-tree
				blockNode
				show-icon
				v-model:expandedKeys="expandedKeys"
				v-model:selectedKeys="selectedKeys"
				v-model:checkedKeys="checkedKeys"
				:tree-data="treeData[0]?.children"
				:field-names="fieldNames"
				@select="onTreeSelect"
			>
				<template #title="{ short_name }">
					<span>{{ short_name }}</span>
				</template>
				<template #switcherIcon="{ dataRef, defaultIcon }">
					<component :is="defaultIcon" v-if="isLevelThree(dataRef.index)" />
				</template>
				<template #icon="{ dataRef }">
					<img v-if="dataRef.children.length" class="sub-org-icon" src="@/assets/images/sub-org-icon.png" />
					<span v-else class="sub-org-icon"></span>
				</template>
			</a-tree>
		</div>
		<div class="right" v-if="subTreeData.length">
			<a-tree
				blockNode
				show-icon
				@select="onTreeSelect"
				:tree-data="subTreeData"
				:field-names="fieldNames"
				v-model:selectedKeys="selectedKeys"
				v-model:checkedKeys="checkedKeys"
			>
				<template #title="{ short_name }">
					<span>{{ short_name }}</span>
				</template>
				<template #switcherIcon="{ defaultIcon }">
					<component :is="defaultIcon" />
				</template>
			</a-tree>
		</div>
	</div>
</template>

<script lang="ts" setup>
import org from '@/store/org'
import { ref, shallowRef, unref, watch, watchEffect } from 'vue'

const emit = defineEmits(['select', 'bread-crumb'])

const props = defineProps({
	initKey: {
		type: Array<any>,
	},
	type: {
		type: Number,
		default: 1,
	},
})

const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])
const treeData = shallowRef<any>([])
const subTreeData = shallowRef<any>([])

const fieldNames = {
	children: 'children',
	title: 'short_name',
	key: 'org_id',
}

function addIndexToChildren(data: any) {
	// 定义递归函数
	function addIndexRecursive(children: any, index: any) {
		children.forEach((child: any, i: any) => {
			// 为当前对象添加 index 属性
			child.index = index + '-' + i
			// 如果当前对象的 children 属性存在且是一个数组，则继续递归调用 addIndexRecursive
			if (child.children && Array.isArray(child.children)) {
				addIndexRecursive(child.children, child.index)
			}
		})
	}

	// 遍历顶层对象，为其 children 添加 index
	data.forEach((item: any, i: any) => {
		item.index = String(i)
		if (item.children && Array.isArray(item.children)) {
			addIndexRecursive(item.children, item.index)
		}
	})

	return data
}
function findPath(orgTree: any, targetOrgId: any, targetName: any) {
	let path: any = []

	// 定义递归函数
	function findPathRecursive(node: any, currentPath: any) {
		// 将当前节点添加到当前路径中
		currentPath.push({ org_id: node.org_id, index: node.index, children: node.children, name: node.name })

		// 如果当前节点的 org_id 和 name 匹配目标值，则将当前路径作为结果路径
		if (node.org_id === targetOrgId && node.name === targetName) {
			path = currentPath.slice() // 复制当前路径
			return
		}

		// 遍历当前节点的子节点
		if (node.children) {
			for (const child of node.children) {
				// 递归调用
				findPathRecursive(child, currentPath.slice())
				// 如果找到了路径，则直接返回
				if (path.length > 0) {
					return
				}
			}
		}
	}

	// 调用递归函数
	findPathRecursive(orgTree, [])

	return path
}

const isLevelThree = (index: string) => {
	return getLength(index) < 3
}

const getLength = (index: string) => {
	return index.split('-').length
}

const onTopSelect = () => {
	subTreeData.value = []
	expandedKeys.value = []
	selectedKeys.value = [treeData.value[0].org_id]
	emit('select', treeData.value[0].org_id, treeData.value[0], [])

	emit(
		'bread-crumb', // 寻路并生成结果
		findPath(treeData.value[0], treeData.value[0].org_id, treeData.value[0].name)
	)
}
const currentSelect = ref()

const onTreeSelect = (_selectedKeys: any, { node }: any, type = 'click') => {
	console.log('🚀 ~ onTreeSelect ~ node:', node)
	const len = getLength(node.dataRef.index)

	if (len < 3 || len > 3) {
		len < 3 && (subTreeData.value = [])

		const expKeys = unref(expandedKeys)

		if (!expKeys.includes(node.org_id)) {
			expandedKeys.value.push(node.org_id)
		} else {
			expandedKeys.value = expKeys.filter((item: any) => {
				return item != node.org_id
			})
		}
	} else if (len === 3) {
		subTreeData.value = node.dataRef.children || []
	}
	if (currentSelect.value == _selectedKeys[0]) {
		return
	}

	currentSelect.value = _selectedKeys[0]

	if (node?.dataRef?.org_id === 1) {
		return onTopSelect()
	}

	if (_selectedKeys.length === 0) return

	emit('select', _selectedKeys[0], node.dataRef, expandedKeys.value)

	emit(
		'bread-crumb', // 寻路并生成结果
		findPath(treeData.value[0], _selectedKeys[0], node.dataRef.name)
	)
	// 通过api方式调用的
	console.log(type)
	if (type !== 'click') {
		selectedKeys.value = _selectedKeys
		checkedKeys.value = _selectedKeys
		expandedKeys.value = [node.dataRef.parent_id]
	}
}
const waitStack: any = []

const onSelectApi = (org_id: number) => {
	const _ = () => {
		const orgList = org.flatData
		const current = orgList.find((item: any) => item.org_id == org_id)
		if (current) {
			onTreeSelect(
				[Number(org_id)],
				{
					node: {
						dataRef: current,
					},
				},
				'api'
			)
		}
	}

	if (treeData.value.length && org.flatData.length) {
		_()
	} else {
		waitStack.push(_)
	}
}

watchEffect(async () => {
	const { orgTree } = org
	if (orgTree.length) {
		treeData.value = addIndexToChildren(orgTree)

		emit(
			'bread-crumb', // 寻路并生成结果
			findPath(treeData.value[0], treeData.value[0].org_id, treeData.value[0].name),
			'1' // 第一次
		)
	}
})

watch(
	() => treeData.value,
	(val) => {
		org.updateFlatData(val)

		if (waitStack.length) {
			waitStack.forEach((item: any) => {
				item()
			})
		}
	},
	{
		deep: true,
		immediate: true,
	}
)

if (!org.orgTree.length) {
	org.loadOrgTree(props.type)
}

watchEffect(() => {
	if (props.initKey) {
		const _keys = props.initKey

		expandedKeys.value = _keys
		selectedKeys.value = _keys
		checkedKeys.value = _keys
	}
})

// 监听 type 属性变化，重新加载组织机构数据
watch(
	() => props.type,
	(newType) => {
		org.loadOrgTree(newType)
	}
)

defineExpose({
	treeData,
	checkedKeys,
	selectedKeys,
	expandedKeys,
	subTreeData,
	onSelectApi,
	onTreeSelect,
})
</script>

<style scoped lang="less">
.top-org {
	margin-bottom: 14px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 500;
	font-size: 24px;
	color: rgba(0, 0, 0, 0.85);
	line-height: 28px;
	font-style: normal;
	cursor: pointer;
	.org-icon {
		margin-right: 15px;
		width: 54px;
		height: 54px;
	}
}
.sub-org-icon {
	width: 25px;
	height: 23px;
}
:deep(.ant-tree-iconEle) {
	margin: 0px 9px 0px 0px;
	width: 25px;
	height: 23px;
}
.tree-list {
	display: flex;
	position: relative;
	min-height: 100%;
	background-color: #fff;
	:deep(.ant-tree) {
		.ant-tree-switcher {
			align-self: center;
		}

		.ant-tree-node-selected {
			background: rgba(0, 142, 255, 0.06);
		}
		.ant-tree-node-content-wrapper {
			padding: 12px 0px;
		}
		.ant-tree-node-content-wrapper {
			display: flex;
			align-items: center;
		}
		.ant-tree-switcher-icon {
			font-size: 13px;
			line-height: 13px;
			vertical-align: middle;
		}
		.ant-tree-title {
			// font-size: 22px;
			font-size: 24px;
			line-height: 24px;
		}
	}
	.left {
		width: 351px;
		border-right: 1px solid rgba(0, 0, 0, 0.12);
	}
	.right {
		position: sticky;
		top: 0px;
		height: 100%;
		width: 275px;
	}
}
</style>
